#!/usr/bin/env python3
"""
Server <PERSON><PERSON> Script
Automatically renews a free server every 1 hour and 1 minute by making API calls.
"""

import requests
import time
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_renew.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Set console encoding to handle unicode
import sys
if sys.platform == 'win32':
    import os
    os.system('chcp 65001 > nul')

logger = logging.getLogger(__name__)

class ServerRenewer:
    def __init__(self):
        # Configure multiple servers for renewal
        self.servers = [
            {
                'name': 'Server 1 (9e26fee7)',
                'url': 'https://gpanel.eternalzero.cloud/api/client/freeservers/9e26fee7-e4cf-4440-bb2a-b2bd698a6075/renew',
                'headers': {
                    'accept': 'application/json',
                    'accept-language': 'en-US,en;q=0.9',
                    'content-type': 'application/json',
                    'origin': 'https://gpanel.eternalzero.cloud',
                    'priority': 'u=1, i',
                    'referer': 'https://gpanel.eternalzero.cloud/server/9e26fee7',
                    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
                    'x-requested-with': 'XMLHttpRequest',
                    'x-xsrf-token': 'eyJpdiI6IlhlcGc0NDBERlpwaEludXBQMVpTaVE9PSIsInZhbHVlIjoiZXNCdmFVdXhzTkUrUFlMeXFhbmcyTmFSMmczTzB3K0xsanpWQ3VmL051Vnc2ckQvWFNJajJobm5Wbkd5eFlnUTFCTGkwU0g1QUs3SHFiSURQT2VkVkk1d0duZzE3czl4c1JnLytmVUVNNTF0MkhnUVZobzk5UmNZb3FaQTR2bjYiLCJtYWMiOiJjMGU2NTk3YTIwYjdiNWQ1YzAzZmY1NGY4NWFmZTliYzAwMWYxNjExNTQzNThiNjM5MTc3ODdmODljMTM2MmVkIiwidGFnIjoiIn0='
                },
                'cookies': {
                    '_ga': 'GA1.1.1762334316.1752479911',
                    '__gads': 'ID=c44f5e6804ccc987:T=1752546899:RT=1752546899:S=ALNI_MaFe4Fry9yy9dE02PIHM1N3zFvr2g',
                    '__gpi': 'UID=000011789d1fa7cf:T=1752546899:RT=1752546899:S=ALNI_MZ1kjDZJdnYp-4fW0Qa65Cm_mN7Fg',
                    '__eoi': 'ID=baaf5f824a1e5ff5:T=1752546899:RT=1752546899:S=AA-AfjZ27Vw8Vq9aCq5yMzwOfeYn',
                    '_ga_3GF2BKV5GH': 'GS2.1.s1752536024$o2$g1$t1752536207$j58$l0$h0',
                    'remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d': 'eyJpdiI6InNQUkxrWDN3RFlQM1pETys4WExxRGc9PSIsInZhbHVlIjoiS3hjMWdpSlM4RVFwai9yZUZFek9DN2FTUmwwYXJ5T0o0eWhMZTRZTk9rSGEzNVk1ZmpRY2dvQkMxQU5qd0c1Nm8wMkNIY3dPV2x4WTkxSkVnWjVzay9idlNKRW1TUHhGRHhmUjhOaXpZVmpTeFZ3dUxtUmFJaGJuUmVad21ENjRKbm0vb2d2aGlOZzd2eDNJbXJOajVZcm10c2dBa0pOVnB3OUVrRFhuLzZwMzgza1FsVzZFREp1T3FHUUw2dDB3L000dXdEdy9GTGZWVTF0N0xXYTNJMno0ZTJXcU9PNExjVlpRU2N3d2piaz0iLCJtYWMiOiI2ZTJiYjExMzBjYWZlODE2YzljZWMyNzI5M2EzYWM4Y2E1NzkxNmE1ZTYyY2M1YjI5Y2E1YmMwZWViZjgyYmVlIiwidGFnIjoiIn0%3D',
                    'XSRF-TOKEN': 'eyJpdiI6IlhlcGc0NDBERlpwaEludXBQMVpTaVE9PSIsInZhbHVlIjoiZXNCdmFVdXhzTkUrUFlMeXFhbmcyTmFSMmczTzB3K0xsanpWQ3VmL051Vnc2ckQvWFNJajJobm5Wbkd5eFlnUTFCTGkwU0g1QUs3SHFiSURQT2VkVkk1d0duZzE3czl4c1JnLytmVUVNNTF0MkhnUVZobzk5UmNZb3FaQTR2bjYiLCJtYWMiOiJjMGU2NTk3YTIwYjdiNWQ1YzAzZmY1NGY4NWFmZTliYzAwMWYxNjExNTQzNThiNjM5MTc3ODdmODljMTM2MmVkIiwidGFnIjoiIn0%3D',
                    'pterodactyl_session': 'eyJpdiI6IjZveW0vWEpzQ29WaUx1ck5PdTNBM1E9PSIsInZhbHVlIjoicFBLOHhhNytwemR4Sk1ncEVLdnNOTUJsaDRGVXIrZnlKWFZsVzdoQTFCQTNrQzhqRFpMN1R3aXREQ2lxeERkRkFPak1ycUF5WG5GdUNpeHRISXcrWlNOek9haGZEeStSR2pSNnNQSjhCeWg0cUNLYWRqRUp0ZDY5a0VvT0pWN0IiLCJtYWMiOiJhYTM3YWQ0ZGFkN2ZjYTYxYWVhYWU2ZGJhYmVmNDM0ZTBlNDAyYWY4MWMxMWU5NzAyODlkNzJjNDk4NzU0Y2JiIiwidGFnIjoiIn0%3D'
                },
                'data': {}
            },
            {
                'name': 'Server 2 (412065e6)',
                'url': 'https://gpanel.eternalzero.cloud/api/client/freeservers/412065e6-d859-45dc-bc2a-6aaa8b25ecd1/renew',
                'headers': {
                    'accept': 'application/json',
                    'accept-language': 'en-US,en;q=0.9',
                    'content-type': 'application/json',
                    'origin': 'https://gpanel.eternalzero.cloud',
                    'priority': 'u=1, i',
                    'referer': 'https://gpanel.eternalzero.cloud/server/412065e6',
                    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
                    'x-requested-with': 'XMLHttpRequest',
                    'x-xsrf-token': 'eyJpdiI6IkgwUTA4YnQ4b1FPM21FZU1KdjRMVkE9PSIsInZhbHVlIjoiaHNEUFVWSGcyOXM4b3dtYmNIbG5CMEZQSCtMZzZQV2ZNQUszVmdXMExQekMrejlnSG1xcXFKaWIxczhDQ2c2NGVyeHRLcnE5NXFIY1d6NzNoNFBBc2xNbFo5dnBZUFphYlZ2Q1FnSkp4UmdhelpuSk5TNTZDY0dDK2FINk5BWkciLCJtYWMiOiIwMGRkN2VhYzdiMmJkOGM4ODkxMWM1MGUxZDNjMjc4ODY5N2ZjNmJhMDNkMjhiYWU5YTg3ODQwMGI3MzEzNGY2IiwidGFnIjoiIn0='
                },
                'cookies': {
                    '_ga': 'GA1.1.1762334316.1752479911',
                    '__gads': 'ID=c44f5e6804ccc987:T=1752546899:RT=1752546899:S=ALNI_MaFe4Fry9yy9dE02PIHM1N3zFvr2g',
                    '__gpi': 'UID=000011789d1fa7cf:T=1752546899:RT=1752546899:S=ALNI_MZ1kjDZJdnYp-4fW0Qa65Cm_mN7Fg',
                    '__eoi': 'ID=baaf5f824a1e5ff5:T=1752546899:RT=1752546899:S=AA-AfjZ27Vw8Vq9aCq5yMzwOfeYn',
                    '_ga_3GF2BKV5GH': 'GS2.1.s1752536024$o2$g1$t1752536207$j58$l0$h0',
                    'remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d': 'eyJpdiI6InZqSFBCUkJSU3VlZlRkYWNhYmhGblE9PSIsInZhbHVlIjoidXNobHNueHBnNzE0Q0VEbDR4b2lKTXVuN1F3VktOQ1ljeDNEcHlWWGpRNXhlc2NRcnVGWFBVOGNybGVmMFRUQnFxNng2S3lzaHNKR1ZpWFNDQmNJbTZoaXR6djJiUFB6UmQ0YjFOaEJaZHUrTjVKWnViMlJVRDIvbzg1ZnBpRkxHM3BWVUZmbFRGTVVHNUQwTjIvTzJleWhRbVB6T1kyN0hCZ05kTTlVdGpEdWM4ZUhzVzh4bXJsbnJSL25sSkY1VmNJOHkzcFdOdDI0dCtIMmxlRERUUXI2WmRjaUc1ODgzOTFLTzFkYnc0bz0iLCJtYWMiOiI0OWY1ZWEyODAxNTA3MThhYjdjNDJiNjdjY2I1MTRlNjQxZWVlYTdmMjYyM2UwNWNmYjI2MmJjNzcwMzRlOGQ1IiwidGFnIjoiIn0%3D',
                    'XSRF-TOKEN': 'eyJpdiI6IkgwUTA4YnQ4b1FPM21FZU1KdjRMVkE9PSIsInZhbHVlIjoiaHNEUFVWSGcyOXM4b3dtYmNIbG5CMEZQSCtMZzZQV2ZNQUszVmdXMExQekMrejlnSG1xcXFKaWIxczhDQ2c2NGVyeHRLcnE5NXFIY1d6NzNoNFBBc2xNbFo5dnBZUFphYlZ2Q1FnSkp4UmdhelpuSk5TNTZDY0dDK2FINk5BWkciLCJtYWMiOiIwMGRkN2VhYzdiMmJkOGM4ODkxMWM1MGUxZDNjMjc4ODY5N2ZjNmJhMDNkMjhiYWU5YTg3ODQwMGI3MzEzNGY2IiwidGFnIjoiIn0%3D',
                    'pterodactyl_session': 'eyJpdiI6IldGWTJ0ZzNKL0YyZlRSblB1NHNBUEE9PSIsInZhbHVlIjoiRldaSUhEWFZoVGxjWjdablppdWRmQ0JCZ0l4NXpPOVFSYjFwODZiNFh0TWtGNW1jelBXZ2tmS3dGak1qZ2RJMGpFMVJwM1lSUi8ySWZ0MVZnS3NZZS82eGtBK2RmVUlhSHB4V0hWcldxcUVxbWVYUHZkRUxLS0t3UUloVUFLM3MiLCJtYWMiOiJiMDQ1MzJiNWVlNzg4MDQxODBmY2YwMDI3NDRjMzBjZThhNjZmMWE2MzI0MWE0MmQxZDk4M2QzNmNhY2FhYmE1IiwidGFnIjoiIn0%3D'
                },
                'data': {}
            }
        ]

        # Analytics configuration
        self.analytics_url = 'https://analytics.wispbyte.com/api/event'
        self.analytics_headers = {
            'sec-ch-ua-platform': '"Windows"',
            'Referer': 'https://wispbyte.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'Content-Type': 'text/plain',
            'sec-ch-ua-mobile': '?0'
        }
        self.analytics_data = '{"n":"pageview","u":"https://wispbyte.com/client/dashboard","d":"wispbyte.com","r":"https://wispbyte.com/client/servers/abe58846/edit?path=/main.py"}'

        # Interval: 1 hour and 1 minute = 61 minutes = 3660 seconds
        self.interval_seconds = 61 * 60

    def make_analytics_request(self):
        """Make the analytics request to simulate dashboard access"""
        try:
            response = requests.post(
                self.analytics_url,
                headers=self.analytics_headers,
                data=self.analytics_data,
                timeout=30
            )

            if response.status_code in [200, 202]:  # 202 = Accepted (common for analytics)
                logger.info(f"[SUCCESS] Analytics request successful! (Status: {response.status_code})")
                return True
            else:
                logger.warning(f"[WARNING] Analytics request failed with status {response.status_code}")
                logger.warning(f"Analytics response text: {response.text}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"[ERROR] Analytics request failed: {e}")
            return False
        except Exception as e:
            logger.error(f"[ERROR] Unexpected analytics error: {e}")
            return False

    def make_server_renewal_request(self, server):
        """Make a renewal request for a specific server"""
        try:
            logger.info(f"Making renewal request for {server['name']}...")

            response = requests.post(
                server['url'],
                headers=server['headers'],
                cookies=server['cookies'],
                json=server['data'],
                timeout=30
            )

            # Log the response
            logger.info(f"{server['name']} - Response status code: {response.status_code}")

            if response.status_code == 200:
                logger.info(f"[SUCCESS] {server['name']} renewal request successful!")
                try:
                    response_data = response.json()
                    logger.info(f"{server['name']} - Response data: {json.dumps(response_data, indent=2)}")
                except json.JSONDecodeError:
                    logger.info(f"{server['name']} - Response text: {response.text}")
                return True
            else:
                logger.warning(f"[WARNING] {server['name']} renewal request returned status {response.status_code}")
                logger.warning(f"{server['name']} - Response text: {response.text}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"[ERROR] Error making renewal request for {server['name']}: {e}")
            return False
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error for {server['name']}: {e}")
            return False

    def make_renewal_request(self):
        """Make renewal requests for all servers and analytics request"""
        logger.info("==================================================")
        logger.info("Starting request cycle...")

        # Track success for each server
        server_results = []

        # Renew each server
        for server in self.servers:
            success = self.make_server_renewal_request(server)
            server_results.append(success)

        # Make analytics request
        logger.info("Making analytics request...")
        analytics_success = self.make_analytics_request()

        # Determine overall result
        renewal_success = any(server_results)  # True if at least one server renewal succeeded
        successful_renewals = sum(server_results)
        total_servers = len(self.servers)

        if renewal_success and analytics_success:
            logger.info(f"[SUCCESS] {successful_renewals}/{total_servers} server renewals and analytics successful!")
        elif renewal_success:
            logger.info(f"[PARTIAL] {successful_renewals}/{total_servers} server renewals successful, analytics failed")
        elif analytics_success:
            logger.warning(f"[PARTIAL] Analytics successful, all {total_servers} server renewals failed")
        else:
            logger.warning(f"[FAILED] All {total_servers} server renewals and analytics failed")

        logger.info("Request cycle completed")
        logger.info("==================================================")

        return renewal_success

    def run_scheduler(self):
        """Run the renewal scheduler"""
        logger.info("[START] Starting server renewal and analytics scheduler...")
        logger.info(f"[SCHEDULE] Will make requests every {self.interval_seconds // 60} minutes")
        logger.info(f"[SERVERS] Configured to renew {len(self.servers)} servers:")
        for i, server in enumerate(self.servers, 1):
            logger.info(f"  {i}. {server['name']}")

        # Make initial request
        self.make_renewal_request()

        # Schedule subsequent requests
        while True:
            try:
                # Calculate next run time by adding 61 minutes to current time
                from datetime import timedelta
                next_run = datetime.now() + timedelta(seconds=self.interval_seconds)
                next_run = next_run.replace(microsecond=0, second=0)

                logger.info(f"[SCHEDULE] Next renewal scheduled for: {next_run}")
                logger.info(f"[SLEEP] Sleeping for {self.interval_seconds} seconds...")

                time.sleep(self.interval_seconds)
                self.make_renewal_request()

            except KeyboardInterrupt:
                logger.info("[STOP] Scheduler stopped by user")
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in scheduler: {e}")
                logger.info(f"[RETRY] Continuing with next iteration...")
                time.sleep(60)  # Wait 1 minute before retrying

def main():
    """Main function"""
    renewer = ServerRenewer()
    renewer.run_scheduler()

if __name__ == "__main__":
    main()